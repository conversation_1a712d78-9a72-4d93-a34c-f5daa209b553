# Server Monitor

A lightweight Go application that monitors server connectivity using ICMP ping and sends email notifications when servers become unreachable.

## Features

- 🚀 Pure Go implementation with no external dependencies
- 🐳 Docker support with minimal Alpine-based image
- 🔔 Email notifications for server status changes
- 🛡️ SQLite database for persistence
- ⚡ RESTful API for managing monitored servers
- 🔄 Configurable check interval

## Prerequisites

- <PERSON><PERSON> and <PERSON>er Compose (recommended)
- Or Go 1.21+ if running directly
- SMTP server for email notifications (optional)

## Quick Start with Docker

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd server-monitor
   ```

2. Copy the example environment file and update with your settings:
   ```bash
   cp .env.example .env
   ```
   Edit the `.env` file with your SMTP details and other configurations.

3. Build and start the application:
   ```bash
   docker-compose up -d --build
   ```

4. The API will be available at `http://localhost:8000`

## Manual Installation

1. Ensure you have Go 1.21+ installed
2. Clone the repository and build the application:
   ```bash
   git clone <repository-url>
   cd server-monitor
   go mod download
   go build -o server-monitor .
   ```

3. Run the application:
   ```bash
   ./server-monitor
   ```

## Configuration

Edit the `.env` file with your settings:

```
# Server Configuration
PORT=8000
DB_PATH=/data/monitor.db
CHECK_INTERVAL=60  # in seconds

# SMTP Configuration (optional)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-email-password
SMTP_FROM=<EMAIL>
SMTP_TO=<EMAIL>

# Server Settings
CHECK_INTERVAL=60  # in seconds
HTTP_TIMEOUT=10    # in seconds
PORT=8080          # Web server port
```

## Running the Application

```bash
go run .
```

The server will start on the specified port (default: 8080).

## API Endpoints

- `GET /api/servers` - List all monitored servers
- `GET /api/servers/:id` - Get details of a specific server
- `POST /api/servers` - Add a new server to monitor
- `PUT /api/servers/:id` - Update a server
- `DELETE /api/servers/:id` - Remove a server from monitoring

### Example: Adding a Server

```bash
curl -X POST http://localhost:8080/api/servers \
  -H "Content-Type: application/json" \
  -d '{"name":"Example Server","url":"https://example.com"}'
```

## Building for Production

```bash
go build -o server-monitor
```

## Running in Production

For production use, consider using a process manager like PM2, Supervisor, or systemd to keep the application running.

## License

MIT

/* 全局样式 */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 主容器 */
.main-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    margin: 20px auto;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    max-width: 1400px;
}

/* 卡片样式 */
.card {
    margin-bottom: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 15px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.card-header {
    font-weight: 600;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
    padding: 20px;
}

/* 状态徽章 */
.status-badge {
    padding: 8px 16px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.status-up {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    animation: pulse-green 2s infinite;
}

.status-down {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    color: white;
    animation: pulse-red 2s infinite;
}

.status-unknown {
    background: linear-gradient(135deg, #6c757d 0%, #adb5bd 100%);
    color: white;
}

/* 动画效果 */
@keyframes pulse-green {
    0%, 100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    50% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
}

@keyframes pulse-red {
    0%, 100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
    50% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 按钮样式 */
.action-buttons .btn {
    padding: 0.4rem 0.8rem;
    margin-right: 8px;
    border-radius: 10px;
    transition: all 0.3s ease;
    border: none;
}

.action-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* 导航栏 */
.navbar {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 15px 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 表格样式 */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.table th {
    border-top: none;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.85rem;
    padding: 20px 15px;
}

.table td {
    padding: 15px;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
}

/* 统计卡片 - 紧凑的左右布局 */
.stats-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    height: auto;
    min-height: 90px;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #007bff, #28a745, #ffc107);
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

.stats-card .card-body {
    padding: 20px;
}

.stats-icon {
    flex-shrink: 0;
}

.stats-icon i {
    font-size: 2.5rem;
}

.stats-content {
    flex-grow: 1;
}

.stats-content h5 {
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.8rem;
}

.stats-content h2 {
    font-size: 2.2rem;
    font-weight: 700;
    line-height: 1;
}

.stats-content .progress {
    height: 4px;
    border-radius: 2px;
}

/* 时间显示 */
.time-ago {
    font-size: 0.8rem;
    color: #6c757d;
    font-style: italic;
}

/* 刷新图标 */
.refresh-icon {
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 10px;
    border-radius: 50%;
    background: rgba(0, 123, 255, 0.1);
}

.refresh-icon:hover {
    transform: rotate(360deg);
    background: rgba(0, 123, 255, 0.2);
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0, 123, 255, 0.3);
    border-radius: 50%;
    border-top-color: #007bff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 移动端服务器卡片样式 - 彻底移除闪烁效果 */
.server-card {
    background: white;
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #dee2e6;
    /* 完全移除所有动画和过渡效果 */
    transition: none !important;
    transform: none !important;
    animation: none !important;
}

/* 移动端不使用hover效果 */
@media (min-width: 769px) {
    .server-card:hover {
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
        transition: all 0.2s ease;
    }
}

.server-card.status-up {
    border-left-color: #28a745;
}

.server-card.status-down {
    border-left-color: #dc3545;
}

.server-card.status-unknown {
    border-left-color: #6c757d;
}

.server-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.server-card-title {
    font-weight: 600;
    font-size: 1.1rem;
    margin: 0;
    color: #333;
}

.server-card-body {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
}

.server-card-item {
    display: flex;
    flex-direction: column;
}

.server-card-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 2px;
}

.server-card-value {
    font-size: 0.9rem;
    font-weight: 500;
    color: #333;
}

.server-card-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.server-card-actions .btn {
    padding: 6px 12px;
    font-size: 0.8rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-container {
        margin: 10px;
        padding: 15px;
        border-radius: 15px;
    }

    .stats-card {
        min-height: 100px;
        padding: 15px 10px;
    }

    .stats-card h2 {
        font-size: 1.8rem;
    }

    .stats-card h5 {
        font-size: 0.75rem;
    }

    /* 隐藏桌面表格，显示移动端卡片 */
    .table-responsive {
        display: none;
    }

    .mobile-servers-container {
        display: block;
    }

    .action-buttons .btn {
        padding: 0.3rem 0.6rem;
        margin-right: 5px;
        margin-bottom: 5px;
    }

    /* 标题在移动端更紧凑 */
    .display-4 {
        font-size: 2rem;
        line-height: 1.2;
        margin-bottom: 0.5rem;
    }

    .lead {
        font-size: 1rem;
        margin-bottom: 1rem;
    }

    /* 移动端页面标题区域优化 */
    .row.mb-5 {
        margin-bottom: 2rem !important;
    }

    .row.mb-5 .col-md-8,
    .row.mb-5 .col-md-4 {
        text-align: center;
        margin-bottom: 1rem;
    }

    .row.mb-5 .col-md-4 {
        order: -1; /* 按钮移到标题上方 */
    }

    /* 移动端卡片头部优化 */
    .card-header {
        padding: 15px 20px;
        text-align: center;
    }

    .card-header .d-flex {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .card-header h5 {
        margin-bottom: 0;
        font-size: 1.1rem;
    }

    .card-header small {
        font-size: 0.85rem;
    }

    .card-header .d-flex.align-items-center {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
        gap: 8px;
    }

    .card-header .badge {
        font-size: 0.7rem;
        padding: 4px 8px;
    }

    .form-check {
        margin: 0;
    }

    .form-check-label {
        font-size: 0.8rem;
    }

    /* 导航栏移动端优化 */
    .navbar-brand {
        font-size: 1.2rem;
    }

    .navbar-text {
        font-size: 0.8rem;
    }

    .navbar .btn {
        padding: 6px 10px;
        font-size: 0.8rem;
    }
}

/* 桌面端隐藏移动端卡片 */
@media (min-width: 769px) {
    .mobile-servers-container {
        display: none;
    }
}

/* 模态框样式 */
.modal-content {
    border-radius: 20px;
    border: none;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.modal-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-radius: 20px 20px 0 0;
    padding: 20px 30px;
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    padding: 20px 30px;
    border-top: 1px solid #dee2e6;
}

/* 表单样式 */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    transform: translateY(-2px);
}

.btn {
    border-radius: 10px;
    padding: 12px 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* 警告样式 */
.alert {
    border-radius: 15px;
    border: none;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* 页面加载动画 */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.page-loader.hidden {
    opacity: 0;
    pointer-events: none;
}

.loader-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

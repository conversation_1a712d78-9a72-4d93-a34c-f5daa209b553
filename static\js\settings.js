document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const emailForm = document.getElementById('emailForm');
    const emailEnabled = document.getElementById('emailEnabled');
    const enabledStatus = document.getElementById('enabledStatus');
    const smtpHost = document.getElementById('smtpHost');
    const smtpPort = document.getElementById('smtpPort');
    const smtpUsername = document.getElementById('smtpUsername');
    const smtpPassword = document.getElementById('smtpPassword');
    const emailFrom = document.getElementById('emailFrom');
    const emailTo = document.getElementById('emailTo');
    const saveEmailBtn = document.getElementById('saveEmailBtn');
    const testEmailBtn = document.getElementById('testEmailBtn');
    const currentTimeElement = document.getElementById('currentTime');
    const pageLoader = document.getElementById('pageLoader');

    // Alert modal
    const alertModal = new bootstrap.Modal(document.getElementById('alertModal'));
    const alertTitle = document.getElementById('alertTitle');
    const alertMessage = document.getElementById('alertMessage');

    // 初始化页面
    initializePage();

    // 页面加载完成后隐藏加载动画
    window.addEventListener('load', function() {
        setTimeout(() => {
            pageLoader.classList.add('hidden');
        }, 500);
    });

    // 更新当前时间
    function updateCurrentTime() {
        const now = new Date();
        currentTimeElement.textContent = now.toLocaleTimeString();
    }

    // 每秒更新时间
    setInterval(updateCurrentTime, 1000);
    updateCurrentTime();

    // 初始化页面
    function initializePage() {
        // 加载邮件设置
        loadEmailSettings();

        // 绑定事件监听器
        bindEventListeners();
    }

    // 绑定事件监听器
    function bindEventListeners() {
        // 保存设置按钮
        saveEmailBtn.addEventListener('click', saveEmailSettings);

        // 测试邮件按钮
        testEmailBtn.addEventListener('click', testEmailSettings);

        // 邮件启用开关
        emailEnabled.addEventListener('change', function() {
            updateEnabledStatus();
            toggleFormFields();
        });
    }

    // 更新启用状态显示
    function updateEnabledStatus() {
        if (emailEnabled.checked) {
            enabledStatus.textContent = 'Enabled';
            enabledStatus.className = 'text-success fw-bold';
        } else {
            enabledStatus.textContent = 'Disabled';
            enabledStatus.className = 'text-danger fw-bold';
        }
    }

    // 切换表单字段启用状态
    function toggleFormFields() {
        const fields = [smtpHost, smtpPort, smtpUsername, smtpPassword, emailFrom, emailTo];
        fields.forEach(field => {
            field.disabled = !emailEnabled.checked;
        });

        testEmailBtn.disabled = !emailEnabled.checked;
    }

    // Load email settings from API
    function loadEmailSettings() {
        fetch('/api/settings/email')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to fetch email settings');
                }
                return response.json();
            })
            .then(config => {
                // Populate form with settings
                emailEnabled.checked = config.enabled;
                smtpHost.value = config.host || '';
                smtpPort.value = config.port || 587;
                smtpUsername.value = config.username || '';
                smtpPassword.value = config.password || '';
                emailFrom.value = config.from || '';
                emailTo.value = config.to || '';

                // Update UI based on settings
                updateEnabledStatus();
                toggleFormFields();
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('Error', 'Failed to load email settings. Please try again later.');
            });
    }

    // Save email settings
    function saveEmailSettings() {
        const emailConfig = {
            enabled: emailEnabled.checked,
            host: smtpHost.value,
            port: parseInt(smtpPort.value) || 587,
            username: smtpUsername.value,
            password: smtpPassword.value,
            from: emailFrom.value,
            to: emailTo.value
        };

        // Validate required fields if enabled
        if (emailConfig.enabled) {
            if (!emailConfig.host) {
                showAlert('Validation Error', 'SMTP Server is required when notifications are enabled.');
                return;
            }
            if (!emailConfig.from) {
                showAlert('Validation Error', 'From Email is required when notifications are enabled.');
                return;
            }
            if (!emailConfig.to) {
                showAlert('Validation Error', 'To Email is required when notifications are enabled.');
                return;
            }
        }

        fetch('/api/settings/email', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(emailConfig)
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.error || 'Failed to save email settings');
                });
            }
            return response.json();
        })
        .then(() => {
            showAlert('Success', 'Email settings saved successfully!');
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Error', error.message);
        });
    }

    // Test email settings
    function testEmailSettings() {
        fetch('/api/settings/email/test', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                host: smtpHost.value,
                port: parseInt(smtpPort.value) || 587,
                username: smtpUsername.value,
                password: smtpPassword.value,
                from: emailFrom.value,
                to: emailTo.value
            })
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.error || 'Failed to send test email');
                });
            }
            return response.json();
        })
        .then(() => {
            showAlert('Success', 'Test email sent successfully!');
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Error', error.message);
        });
    }

    // Show alert modal
    function showAlert(title, message) {
        alertTitle.textContent = title;
        alertMessage.textContent = message;
        alertModal.show();
    }
});

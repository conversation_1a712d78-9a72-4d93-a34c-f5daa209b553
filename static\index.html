<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Monitor Dashboard</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 页面加载动画 -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-spinner"></div>
    </div>

    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-hdd-network me-2"></i>Server Monitor Pro
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">
                            <i class="bi bi-speedometer2 me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/settings.html">
                            <i class="bi bi-gear me-1"></i>Settings
                        </a>
                    </li>
                </ul>
                <div class="navbar-nav">
                    <span class="navbar-text me-3">
                        <i class="bi bi-clock me-1"></i>
                        <span id="currentTime"></span>
                    </span>
                    <button class="btn btn-outline-light btn-sm refresh-icon" id="refreshBtn" title="Refresh Data">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <div class="main-container">
        <div class="row mb-5">
            <div class="col-md-8">
                <h1 class="display-4 fw-bold mb-2">Server Status Dashboard</h1>
                <p class="lead text-muted">Real-time monitoring of your server infrastructure</p>
            </div>
            <div class="col-md-4 text-end d-flex align-items-center justify-content-end">
                <button id="addServerBtn" class="btn btn-success btn-lg" data-bs-toggle="modal" data-bs-target="#serverModal">
                    <i class="bi bi-plus-circle me-2"></i>Add Server
                </button>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="row mb-5">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card stats-card border-0">
                    <div class="card-body d-flex align-items-center">
                        <div class="stats-icon me-3">
                            <i class="bi bi-check-circle-fill text-success"></i>
                        </div>
                        <div class="stats-content">
                            <h5 class="card-title mb-1">Servers Online</h5>
                            <h2 id="serversUp" class="text-success mb-0">-</h2>
                            <div class="progress mt-2" style="height: 4px;">
                                <div class="progress-bar bg-success" id="upProgress" role="progressbar" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card stats-card border-0">
                    <div class="card-body d-flex align-items-center">
                        <div class="stats-icon me-3">
                            <i class="bi bi-x-circle-fill text-danger"></i>
                        </div>
                        <div class="stats-content">
                            <h5 class="card-title mb-1">Servers Offline</h5>
                            <h2 id="serversDown" class="text-danger mb-0">-</h2>
                            <div class="progress mt-2" style="height: 4px;">
                                <div class="progress-bar bg-danger" id="downProgress" role="progressbar" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card stats-card border-0">
                    <div class="card-body d-flex align-items-center">
                        <div class="stats-icon me-3">
                            <i class="bi bi-hdd-stack text-primary"></i>
                        </div>
                        <div class="stats-content">
                            <h5 class="card-title mb-1">Total Servers</h5>
                            <h2 id="serversTotal" class="text-primary mb-0">-</h2>
                            <div class="progress mt-2" style="height: 4px;">
                                <div class="progress-bar bg-primary" role="progressbar" style="width: 100%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 服务器列表 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-0">
                        <i class="bi bi-list-ul me-2"></i>Servers List
                    </h5>
                    <small class="text-muted">Monitor and manage your servers</small>
                </div>
                <div class="d-flex align-items-center">
                    <span class="badge bg-primary me-2" id="lastUpdateBadge">Last updated: Never</span>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                        <label class="form-check-label" for="autoRefresh">Auto Refresh</label>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <!-- 桌面端表格 -->
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th><i class="bi bi-tag me-1"></i>Name</th>
                                <th><i class="bi bi-globe me-1"></i>Host</th>
                                <th><i class="bi bi-activity me-1"></i>Status</th>
                                <th><i class="bi bi-speedometer me-1"></i>Latency</th>
                                <th><i class="bi bi-clock me-1"></i>Interval</th>
                                <th><i class="bi bi-exclamation-triangle me-1"></i>Last Down</th>
                                <th><i class="bi bi-stopwatch me-1"></i>Last Downtime</th>
                                <th><i class="bi bi-calendar-check me-1"></i>Last Updated</th>
                                <th><i class="bi bi-gear me-1"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="serversTableBody">
                            <!-- Server rows will be added here dynamically -->
                        </tbody>
                    </table>
                </div>

                <!-- 移动端卡片容器 -->
                <div class="mobile-servers-container p-3" id="mobileServersContainer">
                    <!-- Mobile server cards will be added here dynamically -->
                </div>

                <div id="noServersMessage" class="alert alert-info m-4 d-none">
                    <div class="text-center">
                        <i class="bi bi-info-circle display-4 text-info mb-3"></i>
                        <h5>No servers configured</h5>
                        <p class="mb-3">Get started by adding your first server to monitor.</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#serverModal">
                            <i class="bi bi-plus-circle me-2"></i>Add Your First Server
                        </button>
                    </div>
                </div>
                <div id="loadingMessage" class="text-center p-4 d-none">
                    <div class="loading me-2"></div>
                    Loading servers...
                </div>
            </div>
        </div>
    </div>

    <!-- Server Modal -->
    <div class="modal fade" id="serverModal" tabindex="-1" aria-labelledby="serverModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="serverModalLabel">Add Server</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="serverForm">
                        <input type="hidden" id="serverId">
                        <div class="mb-3">
                            <label for="serverName" class="form-label">Server Name</label>
                            <input type="text" class="form-control" id="serverName" required>
                        </div>
                        <div class="mb-3">
                            <label for="serverHost" class="form-label">Host (IP or Domain)</label>
                            <input type="text" class="form-control" id="serverHost" required>
                            <div class="form-text">Examples: ***********, example.com</div>
                        </div>
                        <div class="mb-3">
                            <label for="serverCheckInterval" class="form-label">Check Interval (seconds)</label>
                            <input type="number" class="form-control" id="serverCheckInterval" min="1" value="5" required>
                            <div class="form-text">How often to check this server (in seconds)</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveServerBtn">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div class="modal fade" id="confirmationModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Deletion</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    Are you sure you want to delete this server?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Downtime Details Modal -->
    <div class="modal fade" id="downtimeModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="downtimeModalLabel">Downtime History</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Statistics Summary -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-primary" id="totalIncidents">-</h5>
                                    <p class="card-text">Total Incidents</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-warning" id="totalDowntime">-</h5>
                                    <p class="card-text">Total Downtime</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-info" id="averageIncident">-</h5>
                                    <p class="card-text">Average Incident</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-success" id="uptimePercentage">-</h5>
                                    <p class="card-text">Uptime %</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="daysFilter" class="form-label">Time Period</label>
                            <select class="form-select" id="daysFilter">
                                <option value="7">Last 7 days</option>
                                <option value="30" selected>Last 30 days</option>
                                <option value="90">Last 90 days</option>
                                <option value="365">Last year</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="startDate" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="startDate">
                        </div>
                        <div class="col-md-4">
                            <label for="endDate" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="endDate">
                        </div>
                    </div>

                    <!-- Incidents Table -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Start Time</th>
                                    <th>End Time</th>
                                    <th>Duration</th>
                                    <th>Status Before</th>
                                    <th>Status After</th>
                                </tr>
                            </thead>
                            <tbody id="downtimeTableBody">
                                <!-- Downtime incidents will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Loading and No Data Messages -->
                    <div id="downtimeLoading" class="text-center py-4 d-none">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading downtime history...</p>
                    </div>

                    <div id="noDowntimeData" class="text-center py-4 d-none">
                        <i class="bi bi-check-circle-fill text-success" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">No Downtime Incidents</h5>
                        <p class="text-muted">This server has been running smoothly with no recorded downtime incidents in the selected period.</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/app.js"></script>
</body>
</html>

apiVersion: apps/v1
kind: Deployment
metadata:
  name: server-monitor
  labels:
    app: server-monitor
spec:
  replicas: 2
  selector:
    matchLabels:
      app: server-monitor
  template:
    metadata:
      name: server-monitor
      labels:
        app: server-monitor
    spec:
      imagePullSecrets:
      - name: regcred
      containers:
      - name: server-monitor
        image: 192.168.150.253:20080/congeer/server-monitor:1b180cc4
        imagePullPolicy: Always
        ports:
        - name: http
          containerPort: 8000
        env:
        - name: PORT
          value: "8000"
        - name: DB_PATH
          value: "/data/monitor.db"
        - name: CHECK_INTERVAL
          value: "5"
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /api/servers
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/servers
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3

---
apiVersion: v1
kind: Service
metadata:
  name: server-monitor
  labels:
    app: server-monitor
spec:
  type: LoadBalancer
  ports:
    - name: http
      port: 8000
      targetPort: http
      protocol: TCP
  selector:
    app: server-monitor

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: server-monitor-pvc
  labels:
    app: server-monitor
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
  # storageClassName: your-storage-class  # Uncomment and specify if needed

---
# Optional: SMTP Secret for email notifications
# Uncomment and configure if you need email notifications
# apiVersion: v1
# kind: Secret
# metadata:
#   name: smtp-secret
# type: Opaque
# data:
#   username: <base64-encoded-smtp-username>
#   password: <base64-encoded-smtp-password>

---
# Optional: ConfigMap for additional configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: server-monitor-config
  labels:
    app: server-monitor
data:
  CHECK_INTERVAL: "60"
  # Add other non-sensitive configuration here

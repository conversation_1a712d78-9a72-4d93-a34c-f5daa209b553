<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Server Monitor Pro</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 页面加载动画 -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-spinner"></div>
    </div>

    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-hdd-network me-2"></i>Server Monitor Pro
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="bi bi-speedometer2 me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/settings.html">
                            <i class="bi bi-gear me-1"></i>Settings
                        </a>
                    </li>
                </ul>
                <div class="navbar-nav">
                    <span class="navbar-text me-3">
                        <i class="bi bi-clock me-1"></i>
                        <span id="currentTime"></span>
                    </span>
                </div>
            </div>
        </div>
    </nav>

    <div class="main-container">
        <div class="row mb-5">
            <div class="col-12">
                <h1 class="display-4 fw-bold mb-2">
                    <i class="bi bi-gear-fill me-3"></i>Settings
                </h1>
                <p class="lead text-muted">Configure your server monitoring preferences and notifications</p>
            </div>
        </div>

        <!-- Email Notifications Card -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-0">
                        <i class="bi bi-envelope-fill me-2"></i>Email Notifications
                    </h5>
                    <small class="text-muted">Configure email alerts for server status changes</small>
                </div>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="emailEnabled">
                    <label class="form-check-label fw-bold" for="emailEnabled">
                        <span id="enabledStatus">Disabled</span>
                    </label>
                </div>
            </div>
            <div class="card-body">
                <form id="emailForm">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>Note:</strong> Email notifications will be sent when servers go down or come back online.
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="smtpHost" class="form-label">
                                <i class="bi bi-server me-1"></i>SMTP Server
                            </label>
                            <input type="text" class="form-control" id="smtpHost" placeholder="smtp.gmail.com">
                            <div class="form-text">Your email provider's SMTP server address</div>
                        </div>
                        <div class="col-md-6">
                            <label for="smtpPort" class="form-label">
                                <i class="bi bi-plug me-1"></i>SMTP Port
                            </label>
                            <select class="form-control" id="smtpPort">
                                <option value="587">587 (TLS)</option>
                                <option value="465">465 (SSL)</option>
                                <option value="25">25 (Plain)</option>
                                <option value="2525">2525 (Alternative)</option>
                            </select>
                            <div class="form-text">Common ports: 587 (TLS), 465 (SSL)</div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="smtpUsername" class="form-label">
                                <i class="bi bi-person me-1"></i>SMTP Username
                            </label>
                            <input type="text" class="form-control" id="smtpUsername" placeholder="<EMAIL>">
                            <div class="form-text">Usually your email address</div>
                        </div>
                        <div class="col-md-6">
                            <label for="smtpPassword" class="form-label">
                                <i class="bi bi-key me-1"></i>SMTP Password
                            </label>
                            <input type="password" class="form-control" id="smtpPassword" placeholder="••••••••">
                            <div class="form-text">Your email password or app-specific password</div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="emailFrom" class="form-label">
                                <i class="bi bi-send me-1"></i>From Email
                            </label>
                            <input type="email" class="form-control" id="emailFrom" placeholder="<EMAIL>">
                            <div class="form-text">Email address that alerts will be sent from</div>
                        </div>
                        <div class="col-md-6">
                            <label for="emailTo" class="form-label">
                                <i class="bi bi-inbox me-1"></i>To Email
                            </label>
                            <input type="email" class="form-control" id="emailTo" placeholder="<EMAIL>">
                            <div class="form-text">Email address to receive alerts</div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="button" id="testEmailBtn" class="btn btn-outline-primary btn-lg me-md-2">
                            <i class="bi bi-envelope-check me-2"></i>Send Test Email
                        </button>
                        <button type="button" id="saveEmailBtn" class="btn btn-primary btn-lg">
                            <i class="bi bi-save me-2"></i>Save Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Quick Setup Guide -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-question-circle me-2"></i>Quick Setup Guide
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="bi bi-google me-2"></i>Gmail Setup</h6>
                        <ul class="list-unstyled">
                            <li><strong>SMTP Server:</strong> smtp.gmail.com</li>
                            <li><strong>Port:</strong> 587 (TLS)</li>
                            <li><strong>Username:</strong> <EMAIL></li>
                            <li><strong>Password:</strong> App-specific password</li>
                        </ul>
                        <small class="text-muted">
                            <i class="bi bi-info-circle me-1"></i>
                            You need to enable 2FA and create an app-specific password for Gmail.
                        </small>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="bi bi-microsoft me-2"></i>Outlook Setup</h6>
                        <ul class="list-unstyled">
                            <li><strong>SMTP Server:</strong> smtp-mail.outlook.com</li>
                            <li><strong>Port:</strong> 587 (TLS)</li>
                            <li><strong>Username:</strong> <EMAIL></li>
                            <li><strong>Password:</strong> Your account password</li>
                        </ul>
                        <small class="text-muted">
                            <i class="bi bi-info-circle me-1"></i>
                            Use your regular Outlook account credentials.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Modal -->
    <div class="modal fade" id="alertModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="alertTitle">Notification</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="alertMessage">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">OK</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/settings.js"></script>
</body>
</html>

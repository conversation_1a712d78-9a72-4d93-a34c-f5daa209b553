package main

import (
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// getServers returns the list of all servers with enhanced error handling
func getServers(c *gin.Context) {
	servers, err := db.GetAllServers()
	if err != nil {
		log.Printf("Error retrieving servers from database: %v", err)
		c.J<PERSON>(http.StatusInternalServerError, gin.H{
			"error":   "Failed to retrieve servers",
			"details": "Database connection error",
		})
		return
	}

	// Add in-memory status, latency, and last ping time to each server
	cacheMutex.RLock()
	for _, server := range servers {
		if cachedStatus, exists := serverStatusCache[server.ID]; exists {
			server.Status = cachedStatus.Status
			server.Latency = cachedStatus.Latency
			// Use LastPing from cache for UpdatedAt
			server.UpdatedAt = cachedStatus.LastPing
		} else {
			// Default values if not in cache
			server.Status = "unknown"
			server.Latency = 0
			// Keep existing UpdatedAt if no cache entry
		}
	}
	cacheMutex.RUnlock()

	// Add response headers for better caching control
	c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
	c.Header("Pragma", "no-cache")
	c.Header("Expires", "0")

	c.JSON(http.StatusOK, servers)
}

// getServer returns a specific server by ID
func getServer(c *gin.Context) {
	id := c.Param("id")

	server, err := db.GetServer(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve server"})
		return
	}

	if server == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Server not found"})
		return
	}

	// Add in-memory status, latency, and last ping time
	cacheMutex.RLock()
	if cachedStatus, exists := serverStatusCache[server.ID]; exists {
		server.Status = cachedStatus.Status
		server.Latency = cachedStatus.Latency
		// Use LastPing from cache for UpdatedAt
		server.UpdatedAt = cachedStatus.LastPing
	} else {
		// Default values if not in cache
		server.Status = "unknown"
		server.Latency = 0
		// Keep existing UpdatedAt if no cache entry
	}
	cacheMutex.RUnlock()

	c.JSON(http.StatusOK, server)
}

// addServerRequest defines the request body for adding a server
type addServerRequest struct {
	Name          string `json:"name" binding:"required"`
	Host          string `json:"host" binding:"required,hostname|ip"`
	CheckInterval int    `json:"check_interval"`
}

// addServer adds a new server to monitor with enhanced validation and logging
func addServer(c *gin.Context) {
	var req addServerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("API: Invalid request for adding server: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Enhanced validation
	if req.Name == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Server name is required",
		})
		return
	}

	if req.Host == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Server host is required",
		})
		return
	}

	// Set default check interval if not provided
	checkInterval := req.CheckInterval
	if checkInterval < 1 {
		checkInterval = 5 // Default to 5 seconds
	}

	// Validate check interval range
	if checkInterval > 3600 { // Max 1 hour
		checkInterval = 3600
	}

	server := &Server{
		ID:            uuid.New().String(),
		Name:          req.Name,
		Host:          req.Host,
		Status:        "unknown",
		CheckInterval: checkInterval,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	log.Printf("API: Adding new server: %s (%s)", server.Name, server.Host)

	if err := db.SaveServer(server); err != nil {
		log.Printf("Error saving server to database: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to save server",
			"details": "Database error",
		})
		return
	}

	// Initial check in background
	go func() {
		log.Printf("Starting initial check for server: %s", server.Name)
		checkServer(server)
	}()

	log.Printf("API: Successfully added server: %s", server.Name)
	c.JSON(http.StatusCreated, server)
}

// updateServerRequest defines the request body for updating a server
type updateServerRequest struct {
	Name          *string `json:"name,omitempty"`
	Host          *string `json:"host,omitempty"`
	CheckInterval *int    `json:"check_interval,omitempty"`
}

// updateServer updates an existing server
func updateServer(c *gin.Context) {
	id := c.Param("id")

	server, err := db.GetServer(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve server"})
		return
	}

	if server == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Server not found"})
		return
	}

	var req updateServerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	updated := false
	if req.Name != nil && *req.Name != server.Name {
		server.Name = *req.Name
		updated = true
	}
	if req.Host != nil && *req.Host != server.Host {
		server.Host = *req.Host
		updated = true
	}
	if req.CheckInterval != nil {
		// Ensure check interval is at least 1 second
		if *req.CheckInterval < 1 {
			server.CheckInterval = 5 // Default to 5 seconds
		} else {
			server.CheckInterval = *req.CheckInterval
		}
		updated = true
	}

	if updated {
		server.UpdatedAt = time.Now()
		if err := db.SaveServer(server); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update server"})
			return
		}

		// Re-check the server after update
		go checkServer(server)
	}

	c.JSON(http.StatusOK, server)
}

// deleteServer removes a server from monitoring
func deleteServer(c *gin.Context) {
	id := c.Param("id")

	server, err := db.GetServer(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve server"})
		return
	}

	if server == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Server not found"})
		return
	}

	if err := db.DeleteServer(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete server"})
		return
	}

	c.Status(http.StatusNoContent)
}

// getEmailSettings returns the current email notification settings
func getEmailSettings(c *gin.Context) {
	config, err := db.GetEmailConfig()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve email settings"})
		return
	}

	c.JSON(http.StatusOK, config)
}

// emailSettingsRequest defines the request body for updating email settings
type emailSettingsRequest struct {
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
	From     string `json:"from"`
	To       string `json:"to"`
	Enabled  bool   `json:"enabled"`
}

// saveEmailSettings updates the email notification settings
func saveEmailSettings(c *gin.Context) {
	var req emailSettingsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate required fields if enabled
	if req.Enabled {
		if req.Host == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "SMTP Server is required when notifications are enabled"})
			return
		}
		if req.From == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "From Email is required when notifications are enabled"})
			return
		}
		if req.To == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "To Email is required when notifications are enabled"})
			return
		}
	}

	// Create email config
	config := &EmailConfig{
		ID:       "email_config_1", // We only need one email config
		Host:     req.Host,
		Port:     req.Port,
		Username: req.Username,
		Password: req.Password,
		From:     req.From,
		To:       req.To,
		Enabled:  req.Enabled,
	}

	// Save to database
	if err := db.SaveEmailConfig(config); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save email settings"})
		return
	}

	// Update global SMTP config
	smtpConfig.Host = req.Host
	smtpConfig.Port = req.Port
	smtpConfig.Username = req.Username
	smtpConfig.Password = req.Password
	smtpConfig.From = req.From
	smtpConfig.To = req.To

	c.JSON(http.StatusOK, config)
}

// testEmailSettings sends a test email with the provided settings
func testEmailSettings(c *gin.Context) {
	var req emailSettingsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate required fields
	if req.Host == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "SMTP Server is required"})
		return
	}
	if req.From == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "From Email is required"})
		return
	}
	if req.To == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "To Email is required"})
		return
	}

	// Create temporary SMTP config
	tempConfig := SMTPConfig{
		Host:     req.Host,
		Port:     req.Port,
		Username: req.Username,
		Password: req.Password,
		From:     req.From,
		To:       req.To,
	}

	// Send test email
	err := sendTestEmail(tempConfig)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Test email sent successfully"})
}

// getServerDowntime returns downtime incidents for a specific server
func getServerDowntime(c *gin.Context) {
	serverID := c.Param("id")

	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "50")
	offsetStr := c.DefaultQuery("offset", "0")
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid offset parameter"})
		return
	}

	var startDate, endDate *time.Time

	if startDateStr != "" {
		parsed, err := time.Parse(time.RFC3339, startDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format. Use RFC3339 format"})
			return
		}
		startDate = &parsed
	}

	if endDateStr != "" {
		parsed, err := time.Parse(time.RFC3339, endDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format. Use RFC3339 format"})
			return
		}
		endDate = &parsed
	}

	// Get downtime incidents
	incidents, err := db.GetDowntimeIncidents(serverID, limit, offset, startDate, endDate)
	if err != nil {
		log.Printf("Error retrieving downtime incidents for server %s: %v", serverID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve downtime incidents"})
		return
	}

	c.JSON(http.StatusOK, incidents)
}

// getServerDowntimeStats returns downtime statistics for a specific server
func getServerDowntimeStats(c *gin.Context) {
	serverID := c.Param("id")

	// Parse days parameter (default to 30 days)
	daysStr := c.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days < 1 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid days parameter. Must be a positive integer"})
		return
	}

	// Get downtime statistics
	stats, err := db.GetDowntimeStats(serverID, days)
	if err != nil {
		log.Printf("Error retrieving downtime stats for server %s: %v", serverID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve downtime statistics"})
		return
	}

	c.JSON(http.StatusOK, stats)
}

document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const serversTableBody = document.getElementById('serversTableBody');
    const mobileServersContainer = document.getElementById('mobileServersContainer');
    const noServersMessage = document.getElementById('noServersMessage');
    const loadingMessage = document.getElementById('loadingMessage');
    const serverForm = document.getElementById('serverForm');
    const serverModal = new bootstrap.Modal(document.getElementById('serverModal'));
    const confirmationModal = new bootstrap.Modal(document.getElementById('confirmationModal'));
    const saveServerBtn = document.getElementById('saveServerBtn');
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    const serversUpElement = document.getElementById('serversUp');
    const serversDownElement = document.getElementById('serversDown');
    const serversTotalElement = document.getElementById('serversTotal');
    const upProgressElement = document.getElementById('upProgress');
    const downProgressElement = document.getElementById('downProgress');
    const lastUpdateBadge = document.getElementById('lastUpdateBadge');
    const autoRefreshCheckbox = document.getElementById('autoRefresh');
    const refreshBtn = document.getElementById('refreshBtn');
    const currentTimeElement = document.getElementById('currentTime');
    const pageLoader = document.getElementById('pageLoader');

    // Server being edited or deleted
    let currentServerId = null;
    let refreshInterval = null;
    let isLoading = false;

    // 初始化页面
    initializePage();

    // 页面加载完成后隐藏加载动画
    window.addEventListener('load', function() {
        setTimeout(() => {
            pageLoader.classList.add('hidden');
        }, 500);
    });

    // 更新当前时间
    function updateCurrentTime() {
        const now = new Date();
        currentTimeElement.textContent = now.toLocaleTimeString();
    }

    // 每秒更新时间
    setInterval(updateCurrentTime, 1000);
    updateCurrentTime();

    // 初始化页面
    function initializePage() {
        // 初始数据加载
        loadServers();

        // 设置自动刷新
        setupAutoRefresh();

        // 绑定事件监听器
        bindEventListeners();
    }

    // 设置自动刷新 - 优化刷新频率
    function setupAutoRefresh() {
        if (autoRefreshCheckbox.checked) {
            // 减少刷新间隔到15秒，提升实时性
            refreshInterval = setInterval(loadServers, 15000);
        }
    }

    // 绑定事件监听器
    function bindEventListeners() {
        // 添加服务器按钮
        document.getElementById('addServerBtn').addEventListener('click', () => {
            resetServerForm();
            document.getElementById('serverModalLabel').textContent = 'Add Server';
        });

        // 保存服务器按钮
        saveServerBtn.addEventListener('click', saveServer);

        // 手动刷新按钮
        refreshBtn.addEventListener('click', () => {
            if (!isLoading) {
                loadServers(true); // 标记为手动刷新
            }
        });

        // 自动刷新开关
        autoRefreshCheckbox.addEventListener('change', function() {
            if (this.checked) {
                // 使用更快的刷新间隔
                refreshInterval = setInterval(loadServers, 15000);
            } else {
                if (refreshInterval) {
                    clearInterval(refreshInterval);
                    refreshInterval = null;
                }
            }
        });
    }

    // Load servers from API - 优化性能
    function loadServers(isManualRefresh = false) {
        if (isLoading) return;

        isLoading = true;

        // 只在手动刷新时显示加载状态，自动刷新时保持界面流畅
        if (isManualRefresh) {
            showLoadingState(true);
            // 添加刷新按钮动画
            refreshBtn.querySelector('i').style.animation = 'spin 1s linear infinite';
        }

        // 添加请求超时和缓存控制
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

        fetch('/api/servers', {
            signal: controller.signal,
            headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        })
            .then(response => {
                clearTimeout(timeoutId);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: Failed to fetch servers`);
                }
                return response.json();
            })
            .then(servers => {
                // 只在开发模式下记录详细日志
                if (window.location.hostname === 'localhost') {
                    console.log('API response:', servers.length, 'servers loaded');
                }

                // 使用requestAnimationFrame优化DOM更新
                requestAnimationFrame(() => {
                    displayServers(servers);
                    updateDashboardStats(servers);
                    updateLastUpdateTime();
                });
            })
            .catch(error => {
                clearTimeout(timeoutId);
                if (error.name === 'AbortError') {
                    console.warn('Request timeout - servers loading took too long');
                    showAlert('Request timeout. Please check your connection.', 'warning');
                } else {
                    console.error('Error loading servers:', error);
                    // 只在手动刷新时显示错误，避免自动刷新时频繁弹出错误
                    if (isManualRefresh) {
                        showAlert('Failed to load servers. Please try again.', 'danger');
                    }
                }
            })
            .finally(() => {
                isLoading = false;
                if (isManualRefresh) {
                    showLoadingState(false);
                    // 停止刷新按钮动画
                    refreshBtn.querySelector('i').style.animation = '';
                }
            });
    }

    // 显示/隐藏加载状态
    function showLoadingState(show) {
        if (show) {
            loadingMessage.classList.remove('d-none');
            serversTableBody.style.opacity = '0.5';
        } else {
            loadingMessage.classList.add('d-none');
            serversTableBody.style.opacity = '1';
        }
    }

    // 更新最后更新时间
    function updateLastUpdateTime() {
        const now = new Date();
        lastUpdateBadge.textContent = `Last updated: ${now.toLocaleTimeString()}`;
    }

    // Display servers in the table and mobile cards
    function displayServers(servers) {
        // 使用DocumentFragment减少DOM操作，避免闪烁
        const tableFragment = document.createDocumentFragment();
        const mobileFragment = document.createDocumentFragment();

        if (servers.length === 0) {
            noServersMessage.classList.remove('d-none');
            serversTableBody.innerHTML = '';
            mobileServersContainer.innerHTML = '';
            return;
        }

        noServersMessage.classList.add('d-none');

        servers.forEach(server => {
            const row = document.createElement('tr');

            // Determine status class
            let statusClass = 'status-unknown';
            // Initialize status if not present
            if (!server.status) {
                server.status = 'unknown';
            }

            if (server.status === 'up') {
                statusClass = 'status-up';
            } else if (server.status === 'down') {
                statusClass = 'status-down';
            }

            // Format date
            let updatedDate = new Date();
            let timeAgo = 'just now';

            // Check if updated_at is valid
            if (server.updated_at) {
                try {
                    updatedDate = new Date(server.updated_at);
                    if (!isNaN(updatedDate.getTime())) {
                        timeAgo = getTimeAgo(updatedDate);
                    }
                } catch (e) {
                    console.error('Error parsing updated_at date:', e);
                }
            }

            // Handle possible undefined values
            if (server.latency === undefined || server.latency === null) {
                server.latency = 0;
            }

            // 对于UP状态的服务器，始终显示延迟值（包括0）
            let latencyDisplay = '-';
            if (server.status === 'up') {
                const latency = parseFloat(server.latency) || 0;
                latencyDisplay = `${latency.toFixed(2)} ms`;
            }

            const checkInterval = (server.check_interval !== undefined && server.check_interval !== null)
                ? `${server.check_interval} sec`
                : '5 sec';

            // Format last downtime
            let lastDownDisplay = '-';

            // Check if last_down is valid
            if (server.last_down && server.last_down !== '0001-01-01T00:00:00Z') {
                try {
                    const lastDownDate = new Date(server.last_down);

                    // Check if the date is valid
                    if (!isNaN(lastDownDate.getTime())) {
                        if (server.status === 'down') {
                            const downSince = getHumanReadableTime(lastDownDate);
                            lastDownDisplay = `<span title="${lastDownDate.toLocaleString()}">${downSince}</span>`;
                        } else {
                            lastDownDisplay = `<span title="${lastDownDate.toLocaleString()}">Last: ${getTimeAgo(lastDownDate)}</span>`;
                        }
                        lastDownTooltip = lastDownDate.toLocaleString();
                    }
                } catch (e) {
                    console.error('Error parsing date:', e);
                    lastDownDisplay = '-';
                }
            }

            row.innerHTML = `
                <td>${escapeHtml(server.name)}</td>
                <td>${escapeHtml(server.host)}</td>
                <td><span class="status-badge ${statusClass}">${server.status.toUpperCase()}</span></td>
                <td>${latencyDisplay}</td>
                <td>${checkInterval}</td>
                <td>${lastDownDisplay}</td>
                <td>
                    <div>${updatedDate.toLocaleString()}</div>
                    <div class="time-ago">${timeAgo}</div>
                </td>
                <td class="action-buttons">
                    <button class="btn btn-sm btn-outline-primary edit-server" data-id="${server.id}">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger delete-server" data-id="${server.id}">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;

            tableFragment.appendChild(row);

            // Create mobile card
            const mobileCard = document.createElement('div');
            mobileCard.className = `server-card ${statusClass}`;

            const latencyMobile = server.status === 'up' ?
                `${(parseFloat(server.latency) || 0).toFixed(2)} ms` : '-';

            const checkIntervalMobile = (server.check_interval !== undefined && server.check_interval !== null)
                ? `${server.check_interval}s` : '5s';

            let lastDownMobile = '-';
            if (server.last_down && server.last_down !== '0001-01-01T00:00:00Z') {
                try {
                    const lastDownDate = new Date(server.last_down);
                    if (!isNaN(lastDownDate.getTime())) {
                        lastDownMobile = server.status === 'down' ?
                            getHumanReadableTime(lastDownDate) :
                            `Last: ${getTimeAgo(lastDownDate)}`;
                    }
                } catch (e) {
                    lastDownMobile = '-';
                }
            }

            mobileCard.innerHTML = `
                <div class="server-card-header">
                    <h6 class="server-card-title">${escapeHtml(server.name)}</h6>
                    <span class="status-badge ${statusClass}">${server.status.toUpperCase()}</span>
                </div>
                <div class="server-card-body">
                    <div class="server-card-item">
                        <div class="server-card-label">Host</div>
                        <div class="server-card-value">${escapeHtml(server.host)}</div>
                    </div>
                    <div class="server-card-item">
                        <div class="server-card-label">Latency</div>
                        <div class="server-card-value">${latencyMobile}</div>
                    </div>
                    <div class="server-card-item">
                        <div class="server-card-label">Check Interval</div>
                        <div class="server-card-value">${checkIntervalMobile}</div>
                    </div>
                    <div class="server-card-item">
                        <div class="server-card-label">Last Updated</div>
                        <div class="server-card-value">${timeAgo}</div>
                    </div>
                    <div class="server-card-item">
                        <div class="server-card-label">Last Down</div>
                        <div class="server-card-value">${lastDownMobile}</div>
                    </div>
                </div>
                <div class="server-card-actions">
                    <button class="btn btn-sm btn-outline-primary edit-server" data-id="${server.id}">
                        <i class="bi bi-pencil me-1"></i>Edit
                    </button>
                    <button class="btn btn-sm btn-outline-danger delete-server" data-id="${server.id}">
                        <i class="bi bi-trash me-1"></i>Delete
                    </button>
                </div>
            `;

            mobileFragment.appendChild(mobileCard);
        });

        // 一次性更新DOM，减少重绘和闪烁
        serversTableBody.innerHTML = '';
        mobileServersContainer.innerHTML = '';
        serversTableBody.appendChild(tableFragment);
        mobileServersContainer.appendChild(mobileFragment);

        // Add event listeners to edit and delete buttons
        document.querySelectorAll('.edit-server').forEach(button => {
            button.addEventListener('click', (e) => {
                const serverId = e.currentTarget.getAttribute('data-id');
                editServer(serverId);
            });
        });

        document.querySelectorAll('.delete-server').forEach(button => {
            button.addEventListener('click', (e) => {
                const serverId = e.currentTarget.getAttribute('data-id');
                showDeleteConfirmation(serverId);
            });
        });
    }

    // Update dashboard statistics
    function updateDashboardStats(servers) {
        const total = servers.length;
        const up = servers.filter(server => server.status === 'up').length;
        const down = servers.filter(server => server.status === 'down').length;

        // 更新数字
        serversUpElement.textContent = up;
        serversDownElement.textContent = down;
        serversTotalElement.textContent = total;

        // 更新进度条
        if (total > 0) {
            const upPercentage = (up / total) * 100;
            const downPercentage = (down / total) * 100;

            upProgressElement.style.width = `${upPercentage}%`;
            downProgressElement.style.width = `${downPercentage}%`;
        } else {
            upProgressElement.style.width = '0%';
            downProgressElement.style.width = '0%';
        }

        // 简化的调试信息
        console.log(`Stats: Total: ${total}, Up: ${up}, Down: ${down}`);
    }

    // 移除了数字动画效果以避免平均延迟乱跳的问题

    // Edit server
    function editServer(serverId) {
        fetch(`/api/servers/${serverId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to fetch server details');
                }
                return response.json();
            })
            .then(server => {
                document.getElementById('serverModalLabel').textContent = 'Edit Server';
                document.getElementById('serverId').value = server.id;
                document.getElementById('serverName').value = server.name;
                document.getElementById('serverHost').value = server.host;
                document.getElementById('serverCheckInterval').value = server.check_interval || 5;

                currentServerId = server.id;
                serverModal.show();
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('Failed to load server details.', 'danger');
            });
    }

    // Save server (create or update)
    function saveServer() {
        if (!serverForm.checkValidity()) {
            serverForm.reportValidity();
            return;
        }

        const serverId = document.getElementById('serverId').value;
        const serverName = document.getElementById('serverName').value;
        const serverHost = document.getElementById('serverHost').value;

        const serverCheckInterval = parseInt(document.getElementById('serverCheckInterval').value) || 5;

        const serverData = {
            name: serverName,
            host: serverHost,
            check_interval: serverCheckInterval
        };

        let url = '/api/servers';
        let method = 'POST';

        if (serverId) {
            url = `/api/servers/${serverId}`;
            method = 'PUT';
        }

        fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(serverData)
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.error || 'Failed to save server');
                });
            }
            return response.json();
        })
        .then(() => {
            serverModal.hide();
            loadServers();
            showAlert(`Server ${serverId ? 'updated' : 'added'} successfully!`, 'success');
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert(error.message, 'danger');
        });
    }

    // Show delete confirmation
    function showDeleteConfirmation(serverId) {
        currentServerId = serverId;
        confirmationModal.show();

        // Set up one-time event listener for delete confirmation
        confirmDeleteBtn.onclick = function() {
            deleteServer(currentServerId);
            confirmationModal.hide();
        };
    }

    // Delete server
    function deleteServer(serverId) {
        fetch(`/api/servers/${serverId}`, {
            method: 'DELETE'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to delete server');
            }
            loadServers();
            showAlert('Server deleted successfully!', 'success');
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Failed to delete server.', 'danger');
        });
    }

    // Reset server form
    function resetServerForm() {
        serverForm.reset();
        document.getElementById('serverId').value = '';
        currentServerId = null;
    }

    // Show alert message
    function showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3`;
        alertContainer.style.zIndex = 9999;
        alertContainer.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        document.body.appendChild(alertContainer);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alertContainer);
            bsAlert.close();
        }, 5000);
    }

    // Helper function to format time ago
    function getTimeAgo(date) {
        const now = new Date();
        const seconds = Math.floor((now - date) / 1000);

        if (seconds < 60) {
            return `${seconds} second${seconds !== 1 ? 's' : ''} ago`;
        }

        const minutes = Math.floor(seconds / 60);
        if (minutes < 60) {
            return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
        }

        const hours = Math.floor(minutes / 60);
        if (hours < 24) {
            return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
        }

        const days = Math.floor(hours / 24);
        if (days < 30) {
            return `${days} day${days !== 1 ? 's' : ''} ago`;
        }

        const months = Math.floor(days / 30);
        if (months < 12) {
            return `${months} month${months !== 1 ? 's' : ''} ago`;
        }

        const years = Math.floor(months / 12);
        return `${years} year${years !== 1 ? 's' : ''} ago`;
    }

    // Helper function to escape HTML
    function escapeHtml(unsafe) {
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    // Helper function to format downtime in days, hours, minutes, seconds
    function getHumanReadableTime(date) {
        const now = new Date();
        let diff = Math.floor((now - date) / 1000); // Difference in seconds

        const days = Math.floor(diff / 86400);
        diff -= days * 86400;

        const hours = Math.floor(diff / 3600);
        diff -= hours * 3600;

        const minutes = Math.floor(diff / 60);
        diff -= minutes * 60;

        const seconds = diff;

        let result = '';
        if (days > 0) result += `${days}d `;
        if (hours > 0 || days > 0) result += `${hours}h `;
        if (minutes > 0 || hours > 0 || days > 0) result += `${minutes}m `;
        result += `${seconds}s`;

        return `Down for ${result}`;
    }
});

package main

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"sync"
	"time"
)

// Server represents a server to monitor
type Server struct {
	ID                   string    `json:"id"`
	Name                 string    `json:"name"`
	Host                 string    `json:"host"`
	Status               string    `json:"status"`
	Latency              float64   `json:"latency"`        // Ping latency in milliseconds
	CheckInterval        int       `json:"check_interval"` // Individual check interval in seconds
	LastDown             time.Time `json:"last_down,omitempty"`
	LastDowntimeDuration int64     `json:"last_downtime_duration"` // Duration of last downtime in seconds
	CreatedAt            time.Time `json:"created_at"`
	UpdatedAt            time.Time `json:"updated_at"`
}

// DowntimeIncident represents a downtime incident for a server
type DowntimeIncident struct {
	ID           string     `json:"id"`
	ServerID     string     `json:"server_id"`
	StartTime    time.Time  `json:"start_time"`
	EndTime      *time.Time `json:"end_time,omitempty"`     // Null if incident is ongoing
	Duration     *int64     `json:"duration,omitempty"`     // Duration in seconds, null if ongoing
	StatusBefore string     `json:"status_before"`          // Status before downtime (usually "up")
	StatusAfter  *string    `json:"status_after,omitempty"` // Status after recovery, null if ongoing
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`
}

// DowntimeStats represents downtime statistics for a server
type DowntimeStats struct {
	TotalIncidents       int        `json:"total_incidents"`
	TotalDowntimeHours   float64    `json:"total_downtime_hours"`
	AverageIncidentHours float64    `json:"average_incident_hours"`
	UptimePercentage     float64    `json:"uptime_percentage"`
	LastIncidentDate     *time.Time `json:"last_incident_date,omitempty"`
}

// EmailConfig represents email notification settings
type EmailConfig struct {
	ID       string `json:"id"`
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
	From     string `json:"from"`
	To       string `json:"to"`
	Enabled  bool   `json:"enabled"`
}

// DB represents the database connection
type DB struct {
	db    *sql.DB
	mutex sync.Mutex // Protects database access
}

// NewDB creates a new database connection
func NewDB(dbPath string) (*DB, error) {
	db, err := sql.Open("sqlite", dbPath)
	if err != nil {
		return nil, err
	}

	// Enable foreign keys
	if _, err := db.Exec("PRAGMA foreign_keys = ON;"); err != nil {
		return nil, err
	}

	// Create tables if they don't exist
	if err := createTables(db); err != nil {
		return nil, err
	}

	return &DB{db: db}, nil
}

// createTables creates the necessary database tables
func createTables(db *sql.DB) error {
	// Create servers table
	_, err := db.Exec(`
	CREATE TABLE IF NOT EXISTS servers (
		id TEXT PRIMARY KEY,
		name TEXT NOT NULL,
		host TEXT NOT NULL,
		check_interval INTEGER DEFAULT 5,
		last_down DATETIME,
		last_downtime_duration INTEGER DEFAULT 0,
		created_at DATETIME NOT NULL,
		updated_at DATETIME NOT NULL
	);
	`)
	if err != nil {
		return fmt.Errorf("failed to create servers table: %w", err)
	}

	// Create downtime_incidents table
	_, err = db.Exec(`
	CREATE TABLE IF NOT EXISTS downtime_incidents (
		id TEXT PRIMARY KEY,
		server_id TEXT NOT NULL,
		start_time DATETIME NOT NULL,
		end_time DATETIME,
		duration INTEGER,
		status_before TEXT NOT NULL,
		status_after TEXT,
		created_at DATETIME NOT NULL,
		updated_at DATETIME NOT NULL,
		FOREIGN KEY (server_id) REFERENCES servers (id) ON DELETE CASCADE
	);
	`)
	if err != nil {
		return fmt.Errorf("failed to create downtime_incidents table: %w", err)
	}

	// Create index on server_id for better query performance
	_, err = db.Exec(`
	CREATE INDEX IF NOT EXISTS idx_downtime_incidents_server_id ON downtime_incidents (server_id);
	`)
	if err != nil {
		return fmt.Errorf("failed to create downtime_incidents index: %w", err)
	}

	// Create index on start_time for better query performance
	_, err = db.Exec(`
	CREATE INDEX IF NOT EXISTS idx_downtime_incidents_start_time ON downtime_incidents (start_time);
	`)
	if err != nil {
		return fmt.Errorf("failed to create downtime_incidents start_time index: %w", err)
	}

	// Create email_config table
	_, err = db.Exec(`
	CREATE TABLE IF NOT EXISTS email_config (
		id TEXT PRIMARY KEY,
		host TEXT NOT NULL,
		port INTEGER NOT NULL,
		username TEXT,
		password TEXT,
		from_email TEXT NOT NULL,
		to_email TEXT NOT NULL,
		enabled INTEGER NOT NULL DEFAULT 0
	);
	`)
	if err != nil {
		return fmt.Errorf("failed to create email_config table: %w", err)
	}

	// Add columns if they don't exist (for backward compatibility)
	// Try to add the last_downtime_duration column if it doesn't exist
	_, _ = db.Exec(`
	ALTER TABLE servers ADD COLUMN last_downtime_duration INTEGER DEFAULT 0;
	`)
	// Ignore error if column already exists

	return nil
}

// Close closes the database connection
func (d *DB) Close() error {
	return d.db.Close()
}

// SaveServer saves a server to the database
func (d *DB) SaveServer(server *Server) error {
	server.UpdatedAt = time.Now()

	// Ensure check interval is at least 1 second
	if server.CheckInterval < 1 {
		server.CheckInterval = 5 // Default to 5 seconds
	}

	// Lock the mutex to prevent concurrent database access
	d.mutex.Lock()
	defer d.mutex.Unlock()

	_, err := d.db.Exec(`
		INSERT OR REPLACE INTO servers (id, name, host, check_interval, last_down, last_downtime_duration, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, COALESCE((SELECT created_at FROM servers WHERE id = ?), ?), ?)
	`, server.ID, server.Name, server.Host, server.CheckInterval, server.LastDown, server.LastDowntimeDuration, server.ID, time.Now(), server.UpdatedAt)

	return err
}

// GetServer retrieves a server by ID
func (d *DB) GetServer(id string) (*Server, error) {
	// Use read lock for queries
	d.mutex.Lock()
	defer d.mutex.Unlock()

	server := &Server{}
	// Initialize with default status
	server.Status = "unknown"

	err := d.db.QueryRow(`
		SELECT id, name, host, check_interval, last_down, last_downtime_duration, created_at, updated_at
		FROM servers WHERE id = ?
	`, id).Scan(
		&server.ID, &server.Name, &server.Host, &server.CheckInterval,
		&server.LastDown, &server.LastDowntimeDuration, &server.CreatedAt, &server.UpdatedAt,
	)

	if err == sql.ErrNoRows {
		return nil, nil
	}

	// Ensure check interval is at least 1 second
	if server.CheckInterval < 1 {
		server.CheckInterval = 5 // Default to 5 seconds
	}

	return server, err
}

// GetAllServers retrieves all servers with improved error handling and logging
func (d *DB) GetAllServers() ([]*Server, error) {
	// Use read lock for queries
	d.mutex.Lock()
	defer d.mutex.Unlock()

	// Add query timeout context
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	rows, err := d.db.QueryContext(ctx, `
		SELECT id, name, host, check_interval, last_down, last_downtime_duration, created_at, updated_at
		FROM servers
		ORDER BY created_at DESC
	`)
	if err != nil {
		log.Printf("Database error in GetAllServers: %v", err)
		return nil, fmt.Errorf("failed to query servers: %w", err)
	}
	defer rows.Close()

	var servers []*Server
	for rows.Next() {
		server := &Server{}
		// Initialize with default status
		server.Status = "unknown"

		err := rows.Scan(
			&server.ID, &server.Name, &server.Host, &server.CheckInterval,
			&server.LastDown, &server.LastDowntimeDuration, &server.CreatedAt, &server.UpdatedAt,
		)
		if err != nil {
			log.Printf("Error scanning server row: %v", err)
			return nil, fmt.Errorf("failed to scan server data: %w", err)
		}

		// Ensure check interval is at least 1 second
		if server.CheckInterval < 1 {
			server.CheckInterval = 5 // Default to 5 seconds
		}

		servers = append(servers, server)
	}

	if err := rows.Err(); err != nil {
		log.Printf("Error iterating server rows: %v", err)
		return nil, fmt.Errorf("error reading server data: %w", err)
	}

	// 移除频繁的数据库日志
	return servers, nil
}

// SaveDowntimeIncident saves a downtime incident to the database
func (d *DB) SaveDowntimeIncident(incident *DowntimeIncident) error {
	log.Printf("DEBUG: SaveDowntimeIncident called - ID: %s, ServerID: %s", incident.ID, incident.ServerID)

	incident.UpdatedAt = time.Now()

	d.mutex.Lock()
	defer d.mutex.Unlock()

	// First, check if the incident already exists
	var existingCreatedAt time.Time
	err := d.db.QueryRow("SELECT created_at FROM downtime_incidents WHERE id = ?", incident.ID).Scan(&existingCreatedAt)

	if err == sql.ErrNoRows {
		// Incident doesn't exist, insert new one
		log.Printf("DEBUG: Inserting new incident %s", incident.ID)
		if incident.CreatedAt.IsZero() {
			incident.CreatedAt = time.Now()
		}

		result, err := d.db.Exec(`
			INSERT INTO downtime_incidents (id, server_id, start_time, end_time, duration, status_before, status_after, created_at, updated_at)
			VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
		`, incident.ID, incident.ServerID, incident.StartTime, incident.EndTime, incident.Duration, incident.StatusBefore, incident.StatusAfter, incident.CreatedAt, incident.UpdatedAt)

		if err != nil {
			log.Printf("ERROR: SQL INSERT failed for incident %s: %v", incident.ID, err)
			return err
		}

		rowsAffected, _ := result.RowsAffected()
		log.Printf("DEBUG: SQL INSERT successful for incident %s, rows affected: %d", incident.ID, rowsAffected)
	} else if err != nil {
		log.Printf("ERROR: Failed to check existing incident %s: %v", incident.ID, err)
		return err
	} else {
		// Incident exists, update it
		log.Printf("DEBUG: Updating existing incident %s", incident.ID)
		incident.CreatedAt = existingCreatedAt // Preserve original created_at

		result, err := d.db.Exec(`
			UPDATE downtime_incidents
			SET server_id = ?, start_time = ?, end_time = ?, duration = ?, status_before = ?, status_after = ?, updated_at = ?
			WHERE id = ?
		`, incident.ServerID, incident.StartTime, incident.EndTime, incident.Duration, incident.StatusBefore, incident.StatusAfter, incident.UpdatedAt, incident.ID)

		if err != nil {
			log.Printf("ERROR: SQL UPDATE failed for incident %s: %v", incident.ID, err)
			return err
		}

		rowsAffected, _ := result.RowsAffected()
		log.Printf("DEBUG: SQL UPDATE successful for incident %s, rows affected: %d", incident.ID, rowsAffected)
	}

	return nil
}

// GetDowntimeIncidents retrieves downtime incidents for a server with optional filtering
func (d *DB) GetDowntimeIncidents(serverID string, limit int, offset int, startDate *time.Time, endDate *time.Time) ([]*DowntimeIncident, error) {
	log.Printf("DEBUG: GetDowntimeIncidents called - ServerID: %s, limit: %d, offset: %d", serverID, limit, offset)

	d.mutex.Lock()
	defer d.mutex.Unlock()

	query := `
		SELECT id, server_id, start_time, end_time, duration, status_before, status_after, created_at, updated_at
		FROM downtime_incidents
		WHERE server_id = ?
	`
	args := []interface{}{serverID}

	if startDate != nil {
		query += " AND start_time >= ?"
		args = append(args, *startDate)
	}

	if endDate != nil {
		query += " AND start_time <= ?"
		args = append(args, *endDate)
	}

	query += " ORDER BY start_time DESC"

	if limit > 0 {
		query += " LIMIT ?"
		args = append(args, limit)
	}

	if offset > 0 {
		query += " OFFSET ?"
		args = append(args, offset)
	}

	log.Printf("DEBUG: Final query: %s", query)
	log.Printf("DEBUG: Query args: %v", args)

	rows, err := d.db.Query(query, args...)
	if err != nil {
		log.Printf("ERROR: Query execution failed: %v", err)
		return nil, fmt.Errorf("failed to query downtime incidents: %w", err)
	}
	defer rows.Close()

	var incidents []*DowntimeIncident
	rowCount := 0
	for rows.Next() {
		rowCount++
		incident := &DowntimeIncident{}
		err := rows.Scan(
			&incident.ID, &incident.ServerID, &incident.StartTime, &incident.EndTime,
			&incident.Duration, &incident.StatusBefore, &incident.StatusAfter,
			&incident.CreatedAt, &incident.UpdatedAt,
		)
		if err != nil {
			log.Printf("ERROR: Failed to scan row %d: %v", rowCount, err)
			return nil, fmt.Errorf("failed to scan downtime incident: %w", err)
		}
		log.Printf("DEBUG: Scanned incident %d - ID: %s, ServerID: %s, StartTime: %s", rowCount, incident.ID, incident.ServerID, incident.StartTime)
		incidents = append(incidents, incident)
	}

	log.Printf("DEBUG: Query returned %d incidents", len(incidents))

	// Ensure we always return a non-nil slice
	if incidents == nil {
		incidents = []*DowntimeIncident{}
	}

	return incidents, nil
}

// GetDowntimeStats calculates downtime statistics for a server
func (d *DB) GetDowntimeStats(serverID string, days int) (*DowntimeStats, error) {
	d.mutex.Lock()
	defer d.mutex.Unlock()

	// Calculate the start date for the statistics period
	startDate := time.Now().AddDate(0, 0, -days)

	// Get total incidents and downtime
	var totalIncidents int
	var totalDowntimeSeconds sql.NullInt64
	var lastIncidentDateStr sql.NullString
	err := d.db.QueryRow(`
		SELECT
			COUNT(*) as total_incidents,
			SUM(COALESCE(duration, 0)) as total_downtime_seconds,
			MAX(start_time) as last_incident_date
		FROM downtime_incidents
		WHERE server_id = ? AND start_time >= ?
	`, serverID, startDate).Scan(&totalIncidents, &totalDowntimeSeconds, &lastIncidentDateStr)

	if err != nil {
		return nil, fmt.Errorf("failed to calculate downtime stats: %w", err)
	}

	stats := &DowntimeStats{
		TotalIncidents: totalIncidents,
	}

	// Parse the last incident date string if valid
	if lastIncidentDateStr.Valid && lastIncidentDateStr.String != "" {
		if parsedTime, parseErr := time.Parse(time.RFC3339, lastIncidentDateStr.String); parseErr == nil {
			stats.LastIncidentDate = &parsedTime
		}
	}

	// Convert seconds to hours
	totalDowntimeHours := 0.0
	if totalDowntimeSeconds.Valid {
		totalDowntimeHours = float64(totalDowntimeSeconds.Int64) / 3600.0
	}
	stats.TotalDowntimeHours = totalDowntimeHours

	// Calculate average incident duration
	if totalIncidents > 0 {
		stats.AverageIncidentHours = totalDowntimeHours / float64(totalIncidents)
	}

	// Calculate uptime percentage
	totalPeriodHours := float64(days * 24)
	if totalPeriodHours > 0 {
		stats.UptimePercentage = ((totalPeriodHours - totalDowntimeHours) / totalPeriodHours) * 100
		if stats.UptimePercentage < 0 {
			stats.UptimePercentage = 0
		}
	} else {
		stats.UptimePercentage = 100
	}

	return stats, nil
}

// GetOngoingDowntimeIncident gets the ongoing downtime incident for a server (if any)
func (d *DB) GetOngoingDowntimeIncident(serverID string) (*DowntimeIncident, error) {
	d.mutex.Lock()
	defer d.mutex.Unlock()

	var incident DowntimeIncident
	err := d.db.QueryRow(`
		SELECT id, server_id, start_time, end_time, duration, status_before, status_after, created_at, updated_at
		FROM downtime_incidents
		WHERE server_id = ? AND end_time IS NULL
		ORDER BY start_time DESC
		LIMIT 1
	`, serverID).Scan(
		&incident.ID, &incident.ServerID, &incident.StartTime, &incident.EndTime,
		&incident.Duration, &incident.StatusBefore, &incident.StatusAfter,
		&incident.CreatedAt, &incident.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil // No ongoing incident found
		}
		return nil, fmt.Errorf("failed to get ongoing downtime incident: %w", err)
	}

	return &incident, nil
}

// DeleteServer removes a server by ID
func (d *DB) DeleteServer(id string) error {
	// Lock the mutex for write operations
	d.mutex.Lock()
	defer d.mutex.Unlock()

	_, err := d.db.Exec("DELETE FROM servers WHERE id = ?", id)
	return err
}

// SaveEmailConfig saves email configuration to the database
func (d *DB) SaveEmailConfig(config *EmailConfig) error {
	// Lock the mutex to prevent concurrent database access
	d.mutex.Lock()
	defer d.mutex.Unlock()

	// If ID is empty, generate a new one
	if config.ID == "" {
		config.ID = "email_config_1" // We only need one email config
	}

	// Convert bool to integer for SQLite
	enabled := 0
	if config.Enabled {
		enabled = 1
	}

	_, err := d.db.Exec(`
		INSERT OR REPLACE INTO email_config (id, host, port, username, password, from_email, to_email, enabled)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?)
	`, config.ID, config.Host, config.Port, config.Username, config.Password, config.From, config.To, enabled)

	return err
}

// GetEmailConfig retrieves email configuration from the database
func (d *DB) GetEmailConfig() (*EmailConfig, error) {
	// Use read lock for queries
	d.mutex.Lock()
	defer d.mutex.Unlock()

	config := &EmailConfig{}
	var enabled int

	err := d.db.QueryRow(`
		SELECT id, host, port, username, password, from_email, to_email, enabled
		FROM email_config LIMIT 1
	`).Scan(
		&config.ID, &config.Host, &config.Port, &config.Username, &config.Password,
		&config.From, &config.To, &enabled,
	)

	if err == sql.ErrNoRows {
		// Return default config if not found
		return &EmailConfig{
			ID:      "email_config_1",
			Host:    "",
			Port:    587,
			Enabled: false,
		}, nil
	}

	// Convert integer to bool
	config.Enabled = (enabled == 1)

	return config, err
}

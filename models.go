package main

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"sync"
	"time"
)

// Server represents a server to monitor
type Server struct {
	ID            string    `json:"id"`
	Name          string    `json:"name"`
	Host          string    `json:"host"`
	Status        string    `json:"status"`
	Latency       float64   `json:"latency"`        // Ping latency in milliseconds
	CheckInterval int       `json:"check_interval"` // Individual check interval in seconds
	LastDown      time.Time `json:"last_down,omitempty"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// EmailConfig represents email notification settings
type EmailConfig struct {
	ID       string `json:"id"`
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
	From     string `json:"from"`
	To       string `json:"to"`
	Enabled  bool   `json:"enabled"`
}

// DB represents the database connection
type DB struct {
	db    *sql.DB
	mutex sync.Mutex // Protects database access
}

// NewDB creates a new database connection
func NewDB(dbPath string) (*DB, error) {
	db, err := sql.Open("sqlite", dbPath)
	if err != nil {
		return nil, err
	}

	// Enable foreign keys
	if _, err := db.Exec("PRAGMA foreign_keys = ON;"); err != nil {
		return nil, err
	}

	// Create tables if they don't exist
	if err := createTables(db); err != nil {
		return nil, err
	}

	return &DB{db: db}, nil
}

// createTables creates the necessary database tables
func createTables(db *sql.DB) error {
	// Create servers table
	_, err := db.Exec(`
	CREATE TABLE IF NOT EXISTS servers (
		id TEXT PRIMARY KEY,
		name TEXT NOT NULL,
		host TEXT NOT NULL,
		check_interval INTEGER DEFAULT 5,
		last_down DATETIME,
		created_at DATETIME NOT NULL,
		updated_at DATETIME NOT NULL
	);
	`)

	// Create email_config table
	_, err = db.Exec(`
	CREATE TABLE IF NOT EXISTS email_config (
		id TEXT PRIMARY KEY,
		host TEXT NOT NULL,
		port INTEGER NOT NULL,
		username TEXT,
		password TEXT,
		from_email TEXT NOT NULL,
		to_email TEXT NOT NULL,
		enabled INTEGER NOT NULL DEFAULT 0
	);
	`)

	// Add columns if they don't exist (for backward compatibility)
	_, err = db.Exec(`
	PRAGMA table_info(servers);
	`)
	if err == nil {
		// Try to add the latency column if it doesn't exist
		_, _ = db.Exec(`
		ALTER TABLE servers ADD COLUMN latency REAL DEFAULT 0;
		`)
		// Ignore error if column already exists

		// Try to add the check_interval column if it doesn't exist
		_, _ = db.Exec(`
		ALTER TABLE servers ADD COLUMN check_interval INTEGER DEFAULT 5;
		`)
		// Ignore error if column already exists
	}
	return err
}

// Close closes the database connection
func (d *DB) Close() error {
	return d.db.Close()
}

// SaveServer saves a server to the database
func (d *DB) SaveServer(server *Server) error {
	server.UpdatedAt = time.Now()

	// Ensure check interval is at least 1 second
	if server.CheckInterval < 1 {
		server.CheckInterval = 5 // Default to 5 seconds
	}

	// Lock the mutex to prevent concurrent database access
	d.mutex.Lock()
	defer d.mutex.Unlock()

	_, err := d.db.Exec(`
		INSERT OR REPLACE INTO servers (id, name, host, check_interval, last_down, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, COALESCE((SELECT created_at FROM servers WHERE id = ?), ?), ?)
	`, server.ID, server.Name, server.Host, server.CheckInterval, server.LastDown, server.ID, time.Now(), server.UpdatedAt)

	return err
}

// GetServer retrieves a server by ID
func (d *DB) GetServer(id string) (*Server, error) {
	// Use read lock for queries
	d.mutex.Lock()
	defer d.mutex.Unlock()

	server := &Server{}
	// Initialize with default status
	server.Status = "unknown"

	err := d.db.QueryRow(`
		SELECT id, name, host, check_interval, last_down, created_at, updated_at
		FROM servers WHERE id = ?
	`, id).Scan(
		&server.ID, &server.Name, &server.Host, &server.CheckInterval,
		&server.LastDown, &server.CreatedAt, &server.UpdatedAt,
	)

	if err == sql.ErrNoRows {
		return nil, nil
	}

	// Ensure check interval is at least 1 second
	if server.CheckInterval < 1 {
		server.CheckInterval = 5 // Default to 5 seconds
	}

	return server, err
}

// GetAllServers retrieves all servers with improved error handling and logging
func (d *DB) GetAllServers() ([]*Server, error) {
	// Use read lock for queries
	d.mutex.Lock()
	defer d.mutex.Unlock()

	// Add query timeout context
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	rows, err := d.db.QueryContext(ctx, `
		SELECT id, name, host, check_interval, last_down, created_at, updated_at
		FROM servers
		ORDER BY created_at DESC
	`)
	if err != nil {
		log.Printf("Database error in GetAllServers: %v", err)
		return nil, fmt.Errorf("failed to query servers: %w", err)
	}
	defer rows.Close()

	var servers []*Server
	for rows.Next() {
		server := &Server{}
		// Initialize with default status
		server.Status = "unknown"

		err := rows.Scan(
			&server.ID, &server.Name, &server.Host, &server.CheckInterval,
			&server.LastDown, &server.CreatedAt, &server.UpdatedAt,
		)
		if err != nil {
			log.Printf("Error scanning server row: %v", err)
			return nil, fmt.Errorf("failed to scan server data: %w", err)
		}

		// Ensure check interval is at least 1 second
		if server.CheckInterval < 1 {
			server.CheckInterval = 5 // Default to 5 seconds
		}

		servers = append(servers, server)
	}

	if err := rows.Err(); err != nil {
		log.Printf("Error iterating server rows: %v", err)
		return nil, fmt.Errorf("error reading server data: %w", err)
	}

	// 移除频繁的数据库日志
	return servers, nil
}

// DeleteServer removes a server by ID
func (d *DB) DeleteServer(id string) error {
	// Lock the mutex for write operations
	d.mutex.Lock()
	defer d.mutex.Unlock()

	_, err := d.db.Exec("DELETE FROM servers WHERE id = ?", id)
	return err
}

// SaveEmailConfig saves email configuration to the database
func (d *DB) SaveEmailConfig(config *EmailConfig) error {
	// Lock the mutex to prevent concurrent database access
	d.mutex.Lock()
	defer d.mutex.Unlock()

	// If ID is empty, generate a new one
	if config.ID == "" {
		config.ID = "email_config_1" // We only need one email config
	}

	// Convert bool to integer for SQLite
	enabled := 0
	if config.Enabled {
		enabled = 1
	}

	_, err := d.db.Exec(`
		INSERT OR REPLACE INTO email_config (id, host, port, username, password, from_email, to_email, enabled)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?)
	`, config.ID, config.Host, config.Port, config.Username, config.Password, config.From, config.To, enabled)

	return err
}

// GetEmailConfig retrieves email configuration from the database
func (d *DB) GetEmailConfig() (*EmailConfig, error) {
	// Use read lock for queries
	d.mutex.Lock()
	defer d.mutex.Unlock()

	config := &EmailConfig{}
	var enabled int

	err := d.db.QueryRow(`
		SELECT id, host, port, username, password, from_email, to_email, enabled
		FROM email_config LIMIT 1
	`).Scan(
		&config.ID, &config.Host, &config.Port, &config.Username, &config.Password,
		&config.From, &config.To, &enabled,
	)

	if err == sql.ErrNoRows {
		// Return default config if not found
		return &EmailConfig{
			ID:      "email_config_1",
			Host:    "",
			Port:    587,
			Enabled: false,
		}, nil
	}

	// Convert integer to bool
	config.Enabled = (enabled == 1)

	return config, err
}

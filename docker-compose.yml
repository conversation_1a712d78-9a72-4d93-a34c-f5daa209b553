version: '3.8'

services:
  server-monitor:
    build: .
    container_name: server-monitor
    restart: unless-stopped
    ports:
      - "8000:8000"
    volumes:
      - ./data:/data
    environment:
      - PORT=8000
      - DB_PATH=/data/monitor.db
      - CHECK_INTERVAL=60
      # SMTP Configuration
      # - SMTP_HOST=smtp.example.com
      # - SMTP_PORT=587
      # - SMTP_USERNAME=<EMAIL>
      # - SMTP_PASSWORD=your-email-password
      # - SMTP_FROM=<EMAIL>
      # - SMTP_TO=<EMAIL>
    cap_add:
      - NET_RAW  # Required for ICMP ping in container

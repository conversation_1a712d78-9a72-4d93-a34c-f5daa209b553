package main

import (
	"net"
	"testing"
)

// TestPingHost_DNS tests the DNS resolution part of pingHost
func TestPingHost_DNS(t *testing.T) {
	// Test cases for DNS resolution
	testCases := []struct {
		name    string
		host    string
		wantErr bool
	}{
		{
			name:    "Valid Host - Google DNS",
			host:    "*******",
			wantErr: false,
		},
		{
			name:    "Valid Host - Google",
			host:    "google.com",
			wantErr: false,
		},
		{
			name:    "Invalid Host",
			host:    "invalid.host.that.does.not.exist.example",
			wantErr: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Test just the DNS resolution part
			host := tc.host
			// Remove protocol if present
			host = trimProtocolAndPort(host)
			
			_, err := net.LookupIP(host)
			if (err != nil) != tc.wantErr {
				t.Errorf("DNS resolution for %s: got error = %v, wantErr %v", host, err, tc.wantErr)
			}
		})
	}
}

// TestTrimProtocolAndPort tests the helper function that cleans host strings
func TestTrimProtocolAndPort(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
	}{
		{"http://example.com:8080", "example.com"},
		{"https://test.org:443", "test.org"},
		{"example.com:22", "example.com"},
		{"plain.host", "plain.host"},
		{"***********:80", "***********"},
	}

	for _, tc := range testCases {
		t.Run(tc.input, func(t *testing.T) {
			result := trimProtocolAndPort(tc.input)
			if result != tc.expected {
				t.Errorf("trimProtocolAndPort(%s) = %s, want %s", tc.input, result, tc.expected)
			}
		})
	}
}

// Helper function to trim protocol and port from host string
func trimProtocolAndPort(host string) string {
	// Remove protocol if present
	host = trimPrefix(host, "http://")
	host = trimPrefix(host, "https://")
	// Remove port if present
	if idx := indexOf(host, ":"); idx >= 0 {
		host = host[:idx]
	}
	return host
}

// Simple string helper functions to avoid importing strings package in tests
func trimPrefix(s, prefix string) string {
	if len(s) >= len(prefix) && s[:len(prefix)] == prefix {
		return s[len(prefix):]
	}
	return s
}

func indexOf(s string, substr string) int {
	for i := 0; i < len(s); i++ {
		if i+len(substr) <= len(s) && s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

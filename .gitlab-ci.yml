# GitLab CI/CD Pipeline for Server Monitor Application

stages:
  - test
  - image

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"

# Docker image build for development/main branch
server-monitor-image-dev:
  stage: image
  tags:
    - k3s
  image:
    name: moby/buildkit:rootless
    entrypoint: [ "sh", "-c" ]
  variables:
    BUILDKITD_FLAGS: --oci-worker-no-process-sandbox
  before_script:
    - |
      mkdir ~/.docker
      echo "{\"auths\":{\"***************:20080\":{\"auth\":\"************************\"}}}" > ~/.docker/config.json
      mkdir -p ~/.config/buildkit
      echo -e "insecure-entitlements = [ \"***************:20080\" ]\n[registry.\"***************:20080\"]\n  http = true" > ~/.config/buildkit/buildkitd.toml
  script:
    - |
      buildctl-daemonless.sh \
          build --frontend=dockerfile.v0 \
          --export-cache type=inline \
          --local context=. \
          --local dockerfile=. \
          --output type=image,name=***************:20080/congeer/server-monitor:${CI_COMMIT_SHORT_SHA},push=true,registry.insecure=true 
  only:
    refs:
      - master
      - develop

# Docker image build for tags (production releases)
server-monitor-image-tags:
  stage: image
  tags:
    - k3s
  image:
    name: moby/buildkit:rootless
    entrypoint: [ "sh", "-c" ]
  variables:
    BUILDKITD_FLAGS: --oci-worker-no-process-sandbox
  before_script:
    - |
      mkdir ~/.docker
      echo "{\"auths\":{\"***************:20080\":{\"auth\":\"************************\"}}}" > ~/.docker/config.json
      mkdir -p ~/.config/buildkit
      echo -e "insecure-entitlements = [ \"***************:20080\" ]\n[registry.\"***************:20080\"]\n  http = true" > ~/.config/buildkit/buildkitd.toml
  script:
    - |
      buildctl-daemonless.sh \
          build --frontend=dockerfile.v0 \
          --export-cache type=inline \
          --local context=. \
          --local dockerfile=. \
          --output type=image,name=***************:20080/congeer/server-monitor:$CI_COMMIT_TAG,push=true,registry.insecure=true 
  only:
    refs:
      - tags

# Optional: Deploy stage for automatic deployment
# deploy-staging:
#   stage: deploy
#   image: alpine:latest
#   before_script:
#     - apk add --no-cache curl
#   script:
#     - echo "Deploying to staging environment"
#     - curl -X POST "http://your-deployment-webhook-url" -d "image=***************:20080/congeer/server-monitor:${CI_COMMIT_SHORT_SHA}"
#   only:
#     refs:
#       - develop
#   when: manual

# deploy-production:
#   stage: deploy
#   image: alpine:latest
#   before_script:
#     - apk add --no-cache curl
#   script:
#     - echo "Deploying to production environment"
#     - curl -X POST "http://your-production-webhook-url" -d "image=***************:20080/congeer/server-monitor:$CI_COMMIT_TAG"
#   only:
#     refs:
#       - tags
#   when: manual

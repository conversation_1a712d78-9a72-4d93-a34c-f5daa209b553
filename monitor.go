package main

import (
	"fmt"
	"log"
	"net"
	"net/smtp"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/go-ping/ping"
)

// SMTPConfig holds SMTP server configuration
type SMTPConfig struct {
	Host     string
	Port     int
	Username string
	Password string
	From     string
	To       string
}

// ServerStatus holds the in-memory status and latency of a server
type ServerStatus struct {
	Status   string    // up, down, or unknown
	Latency  float64   // ping latency in milliseconds
	LastPing time.Time // last time the server was pinged
}

// Global cache to store server statuses in memory
var (
	serverStatusCache = make(map[string]*ServerStatus)
	cacheMutex        = &sync.RWMutex{}
	// Track ongoing downtime incidents
	ongoingIncidents = make(map[string]*DowntimeIncident)
	incidentsMutex   = &sync.RWMutex{}
)

// monitorServers periodically checks the status of all servers with improved performance
func monitorServers() {
	log.Println("Starting server monitoring service...")

	// Use a shorter base interval (500ms) for more responsive monitoring
	ticker := time.NewTicker(500 * time.Millisecond)
	defer ticker.Stop()

	// Map to track when each server was last checked
	lastChecked := make(map[string]time.Time)

	// Channel to limit concurrent checks - increase to 15 for better performance
	checkSemaphore := make(chan struct{}, 15) // Max 15 concurrent checks

	// Initial check
	checkAllServers()

	// Initialize lastChecked map with current time for all servers
	servers, err := db.GetAllServers()
	if err != nil {
		log.Printf("Error getting initial servers list: %v", err)
	} else {
		now := time.Now()
		for _, server := range servers {
			lastChecked[server.ID] = now
		}
		log.Printf("Initialized monitoring for %d servers", len(servers))
	}

	// Periodic checks based on individual server intervals
	tickCount := 0
	for range ticker.C {
		tickCount++

		servers, err := db.GetAllServers()
		if err != nil {
			log.Printf("Error getting servers for monitoring: %v", err)
			continue
		}

		now := time.Now()
		checksScheduled := 0

		for _, server := range servers {
			// Use server's individual check interval, or default if not set
			interval := time.Duration(server.CheckInterval) * time.Second
			if interval < time.Second {
				interval = 5 * time.Second // Default to 5 seconds
			}

			// Check if it's time to check this server
			lastCheck, exists := lastChecked[server.ID]
			if !exists || now.Sub(lastCheck) >= interval {
				// Update last checked time
				lastChecked[server.ID] = now
				checksScheduled++

				// Check the server in a separate goroutine with semaphore
				go func(s *Server) {
					checkSemaphore <- struct{}{}        // Acquire semaphore
					defer func() { <-checkSemaphore }() // Release semaphore

					checkServer(s)
				}(server)
			}
		}

		// Log monitoring activity every 2 minutes (240 ticks at 500ms)
		if tickCount%240 == 0 {
			log.Printf("Monitoring %d servers, scheduled %d checks", len(servers), checksScheduled)
		}
	}
}

func checkAllServers() {
	servers, err := db.GetAllServers()
	if err != nil {
		log.Printf("Error getting servers: %v", err)
		return
	}

	for _, server := range servers {
		go checkServer(server)
	}
}

func checkServer(server *Server) {
	isUp, latency, pingErr := pingHost(server.Host)

	// Get current status from cache or create new entry
	cacheMutex.RLock()
	cachedStatus, exists := serverStatusCache[server.ID]
	cacheMutex.RUnlock()

	if !exists {
		cachedStatus = &ServerStatus{
			Status:   "unknown",
			Latency:  0,
			LastPing: time.Now(),
		}
	}

	previousStatus := cachedStatus.Status
	server.UpdatedAt = time.Now()

	// Update status, latency, and last ping time in memory
	// Always update the last ping time regardless of status
	currentTime := time.Now()

	if isUp {
		// 验证延迟数据的合理性
		if latency < 0 || latency > 30000 { // 超过30秒的延迟视为异常
			log.Printf("Warning: Abnormal latency for server %s: %.2f ms, resetting to 0", server.Name, latency)
			latency = 0 // 重置为0，避免显示异常数据
		}

		// Update cache
		cacheMutex.Lock()
		cachedStatus.Status = "up"
		cachedStatus.Latency = latency
		cachedStatus.LastPing = currentTime
		serverStatusCache[server.ID] = cachedStatus
		cacheMutex.Unlock()

		// Update server object for this request
		server.Status = "up"
		server.Latency = latency

		// Only log status changes to reduce noise
		if previousStatus != "up" {
			log.Printf("Server %s is now UP (latency: %.2f ms)", server.Name, latency)

			// Handle downtime incident recovery
			go handleServerRecovery(server)

			// Send recovery notification
			go sendNotification(server, "up", nil)
		}
	} else {
		// Update cache
		cacheMutex.Lock()
		cachedStatus.Status = "down"
		cachedStatus.Latency = 0
		cachedStatus.LastPing = currentTime
		serverStatusCache[server.ID] = cachedStatus
		cacheMutex.Unlock()

		// Update server object for this request
		server.Status = "down"
		server.Latency = 0
		server.LastDown = time.Now()

		// Only log status changes to reduce noise
		if previousStatus != "down" {
			log.Printf("Server %s is now DOWN", server.Name)

			// Handle downtime incident start
			go handleServerDowntime(server, previousStatus)

			// Send down notification
			go sendNotification(server, "down", pingErr)
		}
	}

	// Only update the database when server goes down to record the last_down time
	if server.Status == "down" && previousStatus != "down" {
		// Only save to database when status changes to down
		if err := db.SaveServer(server); err != nil {
			log.Printf("Error saving server %s: %v", server.Name, err)
		}
	}
}

// pingHost checks if a host is reachable using multiple methods and returns latency in milliseconds
func pingHost(host string) (bool, float64, error) {
	// Remove protocol if present
	host = strings.TrimPrefix(host, "http://")
	host = strings.TrimPrefix(host, "https://")
	// Remove port if present
	host = strings.Split(host, ":")[0]

	// Try to resolve the hostname to an IP address first
	ips, err := net.LookupIP(host)
	if err != nil {
		return false, 0, fmt.Errorf("could not resolve host: %v", err)
	}

	// First try: TCP connection to port 80 (HTTP) or 443 (HTTPS)
	for _, port := range []string{"80", "443"} {
		start := time.Now()
		conn, err := net.DialTimeout("tcp", host+":"+port, 2*time.Second)
		if err == nil {
			latency := float64(time.Since(start).Milliseconds())
			conn.Close()
			return true, latency, nil // Successfully connected with measured latency
		}
	}

	// Second try: Use Go's ping package if TCP connection failed
	// This may not work on all systems due to permission issues
	try := func() (bool, float64, error) {
		pinger, err := ping.NewPinger(host)
		if err != nil {
			return false, 0, fmt.Errorf("could not create pinger: %v", err)
		}

		// On Windows, we need to set privileged mode to false
		if runtime.GOOS == "windows" {
			pinger.SetPrivileged(false)
		}

		// Configure the pinger
		pinger.Count = 3                         // Send three packets for better reliability
		pinger.Timeout = 3 * time.Second         // Wait longer for a reply
		pinger.Interval = 500 * time.Millisecond // 500ms between pings

		// Run the pinger
		err = pinger.Run()
		if err != nil {
			return false, 0, fmt.Errorf("ping failed: %v", err)
		}

		// Get ping statistics
		stats := pinger.Statistics()

		// If we received at least one response, the host is up and we have latency
		return stats.PacketsRecv > 0, float64(stats.AvgRtt.Milliseconds()), nil
	}

	// Try ICMP ping
	isUp, latency, err := try()
	if err == nil && isUp {
		return true, latency, nil
	}

	// If all methods failed, try a simple socket connection to the IP
	for _, ip := range ips {
		// Try to connect to the default gateway port (7)
		start := time.Now()
		conn, err := net.DialTimeout("ip4:icmp", ip.String(), 2*time.Second)
		if err == nil {
			latency := float64(time.Since(start).Milliseconds())
			conn.Close()
			return true, latency, nil
		}
	}

	return false, 0, fmt.Errorf("host unreachable via multiple methods")
}

func sendNotification(server *Server, status string, err error) {
	var subject, body string

	if status == "down" {
		subject = fmt.Sprintf("🔴 Server Down Alert: %s", server.Name)
		body = fmt.Sprintf("Server: %s\nHost: %s\nStatus: DOWN\nTime: %s\nError: %v",
			server.Name, server.Host, time.Now().Format(time.RFC3339), err)
	} else {
		subject = fmt.Sprintf("✅ Server Back Online: %s", server.Name)
		body = fmt.Sprintf("Server: %s\nHost: %s\nStatus: UP\nTime: %s\nDowntime: %s",
			server.Name, server.Host, time.Now().Format(time.RFC3339),
			time.Since(server.LastDown).Round(time.Second).String())
	}

	if err := sendEmail(subject, body); err != nil {
		log.Printf("Failed to send email notification: %v", err)
	}
}

func sendEmail(subject, body string) error {
	// Get current email config
	config, err := db.GetEmailConfig()
	if err != nil {
		return err
	}

	// Check if email notifications are enabled
	if !config.Enabled {
		return nil // Skip sending if disabled
	}

	auth := smtp.PlainAuth("", config.Username, config.Password, config.Host)

	msg := fmt.Sprintf("From: %s\nTo: %s\nSubject: %s\n\n%s",
		config.From, config.To, subject, body)

	addr := fmt.Sprintf("%s:%d", config.Host, config.Port)
	return smtp.SendMail(addr, auth, config.From, []string{config.To}, []byte(msg))
}

// sendTestEmail sends a test email with the provided configuration
func sendTestEmail(config SMTPConfig) error {
	auth := smtp.PlainAuth("", config.Username, config.Password, config.Host)

	subject := "Test Email from Server Monitor"
	body := "This is a test email from your Server Monitor application. If you received this email, your email notification settings are working correctly."

	msg := fmt.Sprintf("From: %s\nTo: %s\nSubject: %s\n\n%s",
		config.From, config.To, subject, body)

	addr := fmt.Sprintf("%s:%d", config.Host, config.Port)
	return smtp.SendMail(addr, auth, config.From, []string{config.To}, []byte(msg))
}

// handleServerDowntime creates a new downtime incident when a server goes down
func handleServerDowntime(server *Server, previousStatus string) {
	incidentsMutex.Lock()
	defer incidentsMutex.Unlock()

	// Check if there's already an ongoing incident for this server
	if _, exists := ongoingIncidents[server.ID]; exists {
		log.Printf("Warning: Server %s already has an ongoing downtime incident", server.Name)
		return
	}

	// Create new downtime incident
	incident := &DowntimeIncident{
		ID:           generateUUID(),
		ServerID:     server.ID,
		StartTime:    time.Now(),
		StatusBefore: previousStatus,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// Store in ongoing incidents map
	ongoingIncidents[server.ID] = incident

	// Save to database
	if err := db.SaveDowntimeIncident(incident); err != nil {
		log.Printf("Error saving downtime incident for server %s: %v", server.Name, err)
	} else {
		log.Printf("Started tracking downtime incident for server %s", server.Name)
	}
}

// handleServerRecovery completes a downtime incident when a server comes back up
func handleServerRecovery(server *Server) {
	incidentsMutex.Lock()
	defer incidentsMutex.Unlock()

	// Check if there's an ongoing incident for this server
	incident, exists := ongoingIncidents[server.ID]
	if !exists {
		log.Printf("Warning: Server %s recovered but no ongoing downtime incident found", server.Name)
		return
	}

	// Complete the incident
	endTime := time.Now()
	duration := int64(endTime.Sub(incident.StartTime).Seconds())
	statusAfter := "up"

	incident.EndTime = &endTime
	incident.Duration = &duration
	incident.StatusAfter = &statusAfter
	incident.UpdatedAt = time.Now()

	// Update server's last downtime duration
	server.LastDowntimeDuration = duration

	// Save updated incident to database
	if err := db.SaveDowntimeIncident(incident); err != nil {
		log.Printf("Error updating downtime incident for server %s: %v", server.Name, err)
	} else {
		log.Printf("Completed downtime incident for server %s (duration: %s)", server.Name, formatDuration(duration))
	}

	// Update server in database with new downtime duration
	if err := db.SaveServer(server); err != nil {
		log.Printf("Error updating server %s with downtime duration: %v", server.Name, err)
	}

	// Remove from ongoing incidents
	delete(ongoingIncidents, server.ID)
}

// generateUUID generates a new UUID string
func generateUUID() string {
	// For now, use a simple timestamp-based ID to avoid import issues
	// In production, you might want to use a proper UUID library
	return fmt.Sprintf("incident_%d", time.Now().UnixNano())
}

// formatDuration formats a duration in seconds to a human-readable string
func formatDuration(seconds int64) string {
	if seconds < 60 {
		return fmt.Sprintf("%d seconds", seconds)
	}

	minutes := seconds / 60
	remainingSeconds := seconds % 60

	if minutes < 60 {
		if remainingSeconds > 0 {
			return fmt.Sprintf("%d minutes %d seconds", minutes, remainingSeconds)
		}
		return fmt.Sprintf("%d minutes", minutes)
	}

	hours := minutes / 60
	remainingMinutes := minutes % 60

	if hours < 24 {
		if remainingMinutes > 0 {
			return fmt.Sprintf("%d hours %d minutes", hours, remainingMinutes)
		}
		return fmt.Sprintf("%d hours", hours)
	}

	days := hours / 24
	remainingHours := hours % 24

	if remainingHours > 0 {
		return fmt.Sprintf("%d days %d hours", days, remainingHours)
	}
	return fmt.Sprintf("%d days", days)
}

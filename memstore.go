package main

import (
	"errors"
	"sync"
)

// MemoryDB is a simple in-memory database implementation
type MemoryDB struct {
	servers     map[string]*Server
	emailConfig *EmailConfig
	mutex       sync.RWMutex
}

// NewMemoryDB creates a new in-memory database
func NewMemoryDB() *MemoryDB {
	return &MemoryDB{
		servers: make(map[string]*Server),
		emailConfig: &EmailConfig{
			ID:      "email_config_1",
			Host:    "",
			Port:    587,
			Enabled: false,
		},
	}
}

// Close is a no-op for memory DB
func (db *MemoryDB) Close() error {
	return nil
}

// SaveServer saves a server to the in-memory database
func (db *MemoryDB) SaveServer(server *Server) error {
	db.mutex.Lock()
	defer db.mutex.Unlock()
	
	db.servers[server.ID] = server
	return nil
}

// GetServer retrieves a server by ID
func (db *MemoryDB) GetServer(id string) (*Server, error) {
	db.mutex.RLock()
	defer db.mutex.RUnlock()
	
	server, exists := db.servers[id]
	if !exists {
		return nil, nil
	}
	return server, nil
}

// GetAllServers retrieves all servers
func (db *MemoryDB) GetAllServers() ([]*Server, error) {
	db.mutex.RLock()
	defer db.mutex.RUnlock()
	
	servers := make([]*Server, 0, len(db.servers))
	for _, server := range db.servers {
		servers = append(servers, server)
	}
	return servers, nil
}

// DeleteServer removes a server by ID
func (db *MemoryDB) DeleteServer(id string) error {
	db.mutex.Lock()
	defer db.mutex.Unlock()
	
	if _, exists := db.servers[id]; !exists {
		return errors.New("server not found")
	}
	
	delete(db.servers, id)
	return nil
}

// SaveEmailConfig saves email configuration to the in-memory database
func (db *MemoryDB) SaveEmailConfig(config *EmailConfig) error {
	db.mutex.Lock()
	defer db.mutex.Unlock()
	
	// If ID is empty, generate a new one
	if config.ID == "" {
		config.ID = "email_config_1" // We only need one email config
	}
	
	db.emailConfig = config
	return nil
}

// GetEmailConfig retrieves email configuration from the in-memory database
func (db *MemoryDB) GetEmailConfig() (*EmailConfig, error) {
	db.mutex.RLock()
	defer db.mutex.RUnlock()
	
	return db.emailConfig, nil
}
